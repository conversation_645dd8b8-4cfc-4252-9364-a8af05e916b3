import Button from "@components/common/Button";
import { useFormik } from "formik";
import { IoIosAddCircleOutline } from "react-icons/io";
import * as Yup from "yup";

interface TaskRow {
  id: number;
  targetTicketNo: string;
  targetTaskType: string;
  targetHours: string;
  actualTicketNo: string;
  actualHours: string;
}

const validationSchema = Yup.object({
  employeeName: Yup.string().required("Employee name is required"),
  position: Yup.string().required("Position is required"),
  monthYear: Yup.string().required("Month/Year is required"),
  weekNo: Yup.string().required("Week number is required"),
  periodFrom: Yup.date().required("Period from is required"),
  periodTo: Yup.date().required("Period to is required"),
  targetSignature: Yup.string().required("Target signature is required"),
  targetDateSigned: Yup.date().required("Target date signed is required"),
  actualSignature: Yup.string().required("Actual signature is required"),
  actualDateSigned: Yup.date().required("Actual date signed is required"),
});

export default function EmployeeReportForm() {
  const taskTypes = ["F-Features", "B-Bugs", "E-Enhancement", "R-Revision", "U-UI/UX", "Q-QA"];

  const initialTasks = [
    {
      id: 1,
      targetTicketNo: "",
      targetTaskType: "",
      targetHours: "",
      actualTicketNo: "",
      actualHours: "",
    },
  ];

  const formik = useFormik({
    initialValues: {
      employeeName: "",
      position: "",
      monthYear: "",
      weekNo: "",
      periodFrom: "",
      periodTo: "",
      targetSignature: "",
      targetDateSigned: "",
      actualSignature: "",
      actualDateSigned: "",
      tasks: initialTasks,
    },
    validationSchema,
    onSubmit: () => {
    // onSubmit: (values) => {
      // For future use
      // console.log("Form submitted:", values);
      // Handle form submission here
    },
  });

  const updateTask = (id: number, field: keyof TaskRow, value: string) => {
    const updatedTasks = formik.values.tasks.map((task) => (task.id === id ? { ...task, [field]: value } : task));
    formik.setFieldValue("tasks", updatedTasks);
  };

  const addTask = () => {
    const newTask: TaskRow = {
      id: formik.values.tasks.length + 1,
      targetTicketNo: "",
      targetTaskType: "",
      targetHours: "",
      actualTicketNo: "",
      actualHours: "",
    };
    formik.setFieldValue("tasks", [...formik.values.tasks, newTask]);
  };

  const removeTask = (id: number) => {
    if (formik.values.tasks.length > 1) {
      const updatedTasks = formik.values.tasks.filter((task) => task.id !== id).map((task, index) => ({ ...task, id: index + 1 }));
      formik.setFieldValue("tasks", updatedTasks);
    }
  };

  const clearForm = () => {
    formik.resetForm();
  };

  const printReport = () => {
    window.print();
  };

  return (
    <div className="card bg-base-100 w-full">
      <div className="card-body">
        <div className="text-center pb-6">
          <h1 className="text-xl font-bold">SYSTEM DEVELOPMENT EMPLOYEE WEEKLY TARGET AND ACCOMPLISHMENT REPORT</h1>
        </div>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Header Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pb-6 border-b">
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Employee Name:</span>
                </label>
                <input
                  type="text"
                  name="employeeName"
                  className={`input input-bordered w-full ${formik.touched.employeeName && formik.errors.employeeName ? "input-error" : ""}`}
                  value={formik.values.employeeName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.employeeName && formik.errors.employeeName && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.employeeName}</span>
                  </label>
                )}
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Position:</span>
                </label>
                <input
                  type="text"
                  name="position"
                  className={`input input-bordered w-full ${formik.touched.position && formik.errors.position ? "input-error" : ""}`}
                  value={formik.values.position}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.position && formik.errors.position && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.position}</span>
                  </label>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Month/Year:</span>
                </label>
                <input
                  type="text"
                  name="monthYear"
                  className={`input input-bordered w-full ${formik.touched.monthYear && formik.errors.monthYear ? "input-error" : ""}`}
                  value={formik.values.monthYear}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.monthYear && formik.errors.monthYear && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.monthYear}</span>
                  </label>
                )}
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Week No:</span>
                </label>
                <input
                  type="text"
                  name="weekNo"
                  className={`input input-bordered w-full ${formik.touched.weekNo && formik.errors.weekNo ? "input-error" : ""}`}
                  value={formik.values.weekNo}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.weekNo && formik.errors.weekNo && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.weekNo}</span>
                  </label>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Period From:</span>
                </label>
                <input
                  type="date"
                  name="periodFrom"
                  className={`input input-bordered w-full ${formik.touched.periodFrom && formik.errors.periodFrom ? "input-error" : ""}`}
                  value={formik.values.periodFrom}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.periodFrom && formik.errors.periodFrom && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.periodFrom}</span>
                  </label>
                )}
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Period To:</span>
                </label>
                <input
                  type="date"
                  name="periodTo"
                  className={`input input-bordered w-full ${formik.touched.periodTo && formik.errors.periodTo ? "input-error" : ""}`}
                  value={formik.values.periodTo}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.touched.periodTo && formik.errors.periodTo && (
                  <label className="label">
                    <span className="label-text-alt text-error">{formik.errors.periodTo}</span>
                  </label>
                )}
              </div>
            </div>
          </div>

          {/* Tasks Table */}
          <div className="overflow-x-auto">
            <table className="table table-bordered w-full border border-base-300">
              {/* Table Header */}
              <thead>
                <tr className="bg-base-100">
                  <th colSpan={3} className="text-center font-semibold border-r border-base-300">
                    TARGET TASKS
                  </th>
                  <th colSpan={2} className="text-center font-semibold">
                    ACTUAL ACCOMPLISHED TASKS
                  </th>
                </tr>
                <tr className="bg-base-200">
                  <th className="text-center text-sm font-medium border-r border-base-300">Ticket No</th>
                  <th className="text-center text-sm font-medium border-r border-base-300">
                    TARGET TASKS
                    <br />
                    <span className="text-xs">(Ticket Type)</span>
                    <br />
                    <span className="text-xs">
                      F-Features, B-Bugs,
                      <br />
                      E-Enhancement, R-Revision,
                      <br />
                      U-UI/UX, Q-QA
                    </span>
                  </th>
                  <th className="text-center text-sm font-medium border-r border-base-300">
                    Target No of Hours
                    <br />
                    to Complete
                  </th>
                  <th className="text-center text-sm font-medium border-r border-base-300">Actual Ticket No</th>
                  <th className="text-center text-sm font-medium">
                    Actual No of Hours
                    <br />
                    Completed
                  </th>
                </tr>
              </thead>

              {/* Task Rows */}
              <tbody>
                {formik.values.tasks.map((task, index) => (
                  <tr key={task.id}>
                    <td className="border-r border-base-300 p-2">
                      <div className="flex items-center">
                        <span className="text-sm font-medium mr-2">{index + 1}.</span>
                        <input type="text" className="input input-sm w-full" value={task.targetTicketNo} onChange={(e) => updateTask(task.id, "targetTicketNo", e.target.value)} />
                      </div>
                    </td>
                    <td className="border-r border-base-300 p-2">
                      <select className="select select-sm w-full" value={task.targetTaskType} onChange={(e) => updateTask(task.id, "targetTaskType", e.target.value)}>
                        <option value="">Select type</option>
                        {taskTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="border-r border-base-300 p-2">
                      <input type="number" className="input input-sm w-full" value={task.targetHours} onChange={(e) => updateTask(task.id, "targetHours", e.target.value)} placeholder="0" />
                    </td>
                    <td className="border-r border-base-300 p-2">
                      <div className="flex items-center">
                        <span className="text-sm font-medium mr-2">{index + 1}.</span>
                        <input type="text" className="input input-sm w-full" value={task.actualTicketNo} onChange={(e) => updateTask(task.id, "actualTicketNo", e.target.value)} />
                      </div>
                    </td>
                    <td className="p-2">
                      <div className="flex items-center gap-2">
                        <input type="number" className="input input-sm flex-1" value={task.actualHours} onChange={(e) => updateTask(task.id, "actualHours", e.target.value)} placeholder="0" />
                        {formik.values.tasks.length > 1 && (
                          <button type="button" className="btn btn-sm btn-error btn-circle" onClick={() => removeTask(task.id)} title="Remove task">
                            ×
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex justify-end pb-6 mt-4">
            <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-sm w-fit flex gap-1 items-center" onClick={addTask}>
              <IoIosAddCircleOutline />
              Add Task
            </Button>
          </div>

          {/* Confirmation Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 pt-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">TARGET CONFIRMATION</h3>
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Employee Signature:</span>
                  </label>
                  <input
                    type="text"
                    name="targetSignature"
                    className={`input input-bordered w-full ${formik.touched.targetSignature && formik.errors.targetSignature ? "input-error" : ""}`}
                    value={formik.values.targetSignature}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.targetSignature && formik.errors.targetSignature && (
                    <label className="label">
                      <span className="label-text-alt text-error">{formik.errors.targetSignature}</span>
                    </label>
                  )}
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Target Date Signed:</span>
                  </label>
                  <input
                    type="date"
                    name="targetDateSigned"
                    className={`input input-bordered w-full ${formik.touched.targetDateSigned && formik.errors.targetDateSigned ? "input-error" : ""}`}
                    value={formik.values.targetDateSigned}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.targetDateSigned && formik.errors.targetDateSigned && (
                    <label className="label">
                      <span className="label-text-alt text-error">{formik.errors.targetDateSigned}</span>
                    </label>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-lg">ACTUAL CONFIRMATION</h3>
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Employee Signature:</span>
                  </label>
                  <input
                    type="text"
                    name="actualSignature"
                    className={`input input-bordered w-full ${formik.touched.actualSignature && formik.errors.actualSignature ? "input-error" : ""}`}
                    value={formik.values.actualSignature}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.actualSignature && formik.errors.actualSignature && (
                    <label className="label">
                      <span className="label-text-alt text-error">{formik.errors.actualSignature}</span>
                    </label>
                  )}
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Actual Date Signed:</span>
                  </label>
                  <input
                    type="date"
                    name="actualDateSigned"
                    className={`input input-bordered w-full ${formik.touched.actualDateSigned && formik.errors.actualDateSigned ? "input-error" : ""}`}
                    value={formik.values.actualDateSigned}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.actualDateSigned && formik.errors.actualDateSigned && (
                    <label className="label">
                      <span className="label-text-alt text-error">{formik.errors.actualDateSigned}</span>
                    </label>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}

          <div className="flex justify-end gap-2">
            <Button variant="custom" onClick={clearForm}>
              Clear Form
            </Button>
            <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full">Save Report</Button>
            <Button classNames="bg-sky-600 hover:bg-sky-700 text-white text-xs mt-4 w-full" onClick={printReport}>
              Print Report
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
