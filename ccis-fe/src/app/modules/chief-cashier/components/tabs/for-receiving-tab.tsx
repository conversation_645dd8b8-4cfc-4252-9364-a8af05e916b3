import { FormStatus } from "@enums/form-status";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import TableFilter from "@modules/treasury/request-pads/table/filter/TableFilter";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useState } from "react";
import { useSelector } from "react-redux";
import { getColumns } from "../table/column/return-pads-column";
import Table from "@components/common/Table";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { TUpdateTransmitalStatus } from "@state/types/form-inventory-transmittal";

const ForReceivingTab = () => {
  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [refreshKey, setRefreshKey] = useState(0);

  // Global State

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const returnPads = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedPads);
  const user = useSelector((state: RootState) => state.auth.user.data);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getReturnedPads, putTransmittalTrail } = useTransmittalFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );

  useFetchWithParams(
    getReturnedPads,
    {
      page,
      pageSize,
      filter: searchText,
      user: user.id,
      statusFilter: FormStatus.RETURNED,
    },
    [searchText, type, page, pageSize, dateFrom, dateTo, refreshKey],
    false
  );

  const getActionEvents = (row: IFormTransmittal): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "Received",
        event: async () => {
          const isConfirmed = await confirmSaveOrEdit("Confirmation", "Do you want to received this pad?");

          if (isConfirmed) {
            putTransmittalTrail({
              id: row?.latestFormTransmittalTrail?.id,
              status: FormStatus.RECEIVED,
            } as TUpdateTransmitalStatus);

            setRefreshKey((prev) => prev + 1);
          }
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents, divisions, areas, formTypes: types });

  const handleClearAll = () => {
    setSearchText("");
    setType(0);
    setDateFrom("");
    setDateTo("");
    setDivisionFilter(0);
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleDivisionChange={handleDivisionChange}
            handleTypeChange={handleTypeChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>

        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          loading={returnPads?.loading}
          data={returnPads?.data || []}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={returnPads?.data?.length || [].length}
          paginationServer={true}
          // loading={loading}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default ForReceivingTab;
