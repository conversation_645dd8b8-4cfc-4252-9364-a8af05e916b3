import { createDate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSelect<PERSON><PERSON>e<PERSON><PERSON><PERSON> } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { RootState } from "@state/reducer";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import TableFilter from "../components/table/filter/TableFilter";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import Typography from "@components/common/Typography";
import Table from "@components/common/Table";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { IActions } from "@interface/common.interface";
import { GoVersions } from "react-icons/go";
import { getColumns } from "../components/table/column/series-tracking-details-column";
import { saveData } from "@helpers/storage";

const StatusTrackingDetails = () => {
  const navigate = useNavigate();

  // Get ID in URL
  const { id } = useParams();

  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  //   const [page, setPage] = useState(1);
  //   const [pageSize, setPageSize] = useState(10);

  // Global State
  const { selectedIncomingReceivedForm: formDetails } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getIncomingReceivedForm } = useIncomingReceivedFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );

  const getActionEvents = (row: IPadAssignments): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: async () => {
          saveData("statusTrackingDetailsID", id);
          navigate(ROUTES.CHIEFCASHIER.seriesDetails.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents });

  const handleClearAll = () => {
    setSearchText("");
    setType(0);
    setDateFrom("");
    setDateTo("");
    setDivisionFilter(0);
    setResetCounter((prev) => prev + 1);
  };

  useEffect(() => {
    if (id) {
      getIncomingReceivedForm({ id: parseInt(id) });
    }
  }, [id]);

  return (
    <div className="p-4">
      <div className="flex justify-start">
        <Button variant="secondary" onClick={() => navigate(ROUTES.CHIEFCASHIER.statusTracking.key)}>
          Back
        </Button>
      </div>
      <Typography className="text-primary text-xl font-semibold uppercase mt-10">SERIES TRACKING DETAILS</Typography>

      <div className="flex w-full flex-col mt-10">
        <div className="divider divider-start font-semibold">SERIES OVERVIEW</div>

        <div className="">
          <div className="grid grid-cols-4 gap-4">
            <div className="flex flex-col gap-2">
              <span className="text-sm text-[#474747CC]">Division</span>
              <Typography size="sm" className="font-semibold">
                {formDetails?.data?.division?.divisionName}
              </Typography>
            </div>
            <div className="flex flex-col gap-2">
              <span className="text-sm text-[#474747CC]">Type</span>
              <Typography size="sm" className="font-semibold">
                {formDetails?.data?.formType?.formTypeCode}
              </Typography>
            </div>
            <div className="flex flex-col gap-2">
              <span className="text-sm text-[#474747CC]">Area Released</span>
              <Typography size="sm" className="font-semibold">
                {formDetails?.data?.area?.areaName}
              </Typography>
            </div>
            <div className="flex flex-col gap-2">
              <span className="text-sm text-[#474747CC]">ATP No.</span>
              <Typography size="sm" className="font-semibold">
                {formDetails?.data?.atpNumber}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-16">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleDivisionChange={handleDivisionChange}
            handleTypeChange={handleTypeChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>
        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          data={formDetails?.data?.padAssignments || []}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={formDetails?.data?.padAssignments?.length || [].length}
          paginationServer={true}
          // loading={loading}
          //   onChangeRowsPerPage={(newPageSize, newPage) => {
          //     setPageSize(newPageSize);
          //     setPage(newPage);
          //   }}
          //   onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default StatusTrackingDetails;
