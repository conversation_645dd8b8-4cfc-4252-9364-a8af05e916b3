import Typography from "@components/common/Typography";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { formatNumber } from "@helpers/format-number";
import { IPadSeriesDetails } from "@interface/form-inventory.interface";
import AttachmentItem from "@modules/gam/components/AttachmentItem";

const IssuedSeries = ({ data }: { data: IPadSeriesDetails }) => {
  return (
    <div className="mt-10 w-full">
      <div className="flex items-end justify-end gap-1 w-full">
        <Typography size="lg">PR No:</Typography>
        <Typography size="3xl" className="text-red-500 font-poppins-medium">
          {data.id}
        </Typography>
      </div>

      <div className="flex flex-col gap-10">
        {/* Assignee Details */}
        <div className="">
          <Typography className="mt-6 divider divider-start font-semibold">Assignee Details</Typography>
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Issued By</span>
              <Typography>
                {data.issuedBy?.firstname} {data.issuedBy?.lastname}
              </Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Remit To</span>
              <Typography>
                {data.remitTo?.firstname} {data.remitTo?.lastname}
              </Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Date Released</span>
              <Typography>{data?.createdAt ? formatWordDateDDMMYYY(data?.createdAt, true) : ""}</Typography>
            </div>
          </div>
        </div>

        {/* PR Details */}
        <div className="">
          <Typography className="mt-6 divider divider-start font-semibold">PR Details</Typography>
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Division</span>
              <Typography>{data.padAssignment?.form?.division.divisionName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Type</span>
              <Typography>{data.padAssignment?.form?.formType.formTypeCode}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Area Released</span>
              <Typography>{data.padAssignment?.form?.area.areaName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Transmittal No.</span>
              <Typography>{data.padAssignment?.formTransmittal?.id}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">ATP No.</span>
              <Typography>{data.padAssignment?.form?.atpNumber}</Typography>
            </div>
          </div>
        </div>

        {/* Coop Details */}
        <div className="">
          <Typography className="mt-6 divider divider-start font-semibold">Coop Details</Typography>
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Coop No.</span>
              <Typography>{data.cooperative?.id}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Coop Name</span>
              <Typography>{data.cooperative?.coopName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Coop Branch</span>
              <Typography>{data.cooperative?.branchName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Product Name</span>
              <Typography>1234</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">PR Date</span>
              <Typography>{data.padAssignment?.formTransmittal?.createdAt ? formatWordDateDDMMYYY(data.padAssignment?.formTransmittal?.createdAt, true) : ""}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Date Created</span>
              <Typography>{data?.releasedAt ? formatWordDateDDMMYYY(data?.releasedAt, true) : ""}</Typography>
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="">
          <Typography className="mt-6 divider divider-start font-semibold">Payment Details</Typography>
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Mode of Payment</span>
              <Typography>{data?.paymentDetail?.paymentMethod.paymentMethodName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Amount</span>
              <Typography>{formatNumber(data.paymentDetail?.amount || 0)}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Date Deposited</span>
              <Typography>{data?.paymentDetail?.dateDeposit ? formatWordDateDDMMYYY(data?.paymentDetail?.dateDeposit, true) : ""}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Bank</span>
              <Typography>{data?.paymentDetail?.bankAccount?.bank?.bankName}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Account Number</span>
              <Typography>{data?.paymentDetail?.bankAccount.bankAccountNumber}</Typography>
            </div>

            <div className="flex flex-col">
              <span className="text-sm text-[#474747CC]">Remarks</span>
              <Typography>{data?.paymentDetail?.remarks}</Typography>
            </div>
          </div>
        </div>

        {/* Attachment Details */}
        <div className="">
          <Typography className="mt-6 divider divider-start font-semibold">Attachments</Typography>
          <div className="grid grid-cols-3 gap-4">
            {data?.attachments &&
              data.attachments.map((file, index) => (
                <div key={index} className="flex items-center gap-2">
                  <AttachmentItem filename={file.label} mimeType={file.mimeType} size={file.size} filepath={file.filepath} />
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IssuedSeries;
