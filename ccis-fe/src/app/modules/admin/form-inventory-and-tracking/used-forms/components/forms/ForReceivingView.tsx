import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IFormTransmittal, IPadAssignments } from "@interface/form-inventory.interface";
import { getReturnedTransmittalTrailService, putFormTransmittalTrailService } from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { useFormik } from "formik";
import { FormStatus } from "@enums/form-status";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";
import Table from "@components/common/Table";
import { getColumns } from "./completed-column";
import { IActions } from "@interface/common.interface";
import { FaEye } from "react-icons/fa";
import ViewPadDetailsModal from "@components/template/Modals/view-pad-details-modal";
import { navigateBack } from "@helpers/navigatorHelper";

const ViewReturnedFormReceiving: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { getUsers } = useUserManagementActions();
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterUser = "";

  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const padDetails = data;
  const [viewPadAssignmentId, setViewPadAssignmentId] = useState<number | undefined>();

  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);

  const getActionEvents = (row: IPadAssignments): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          setViewPadAssignmentId(row.id);
          handleToggleViewModal();
        },
        icon: FaEye,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({
    getActionEvents,
    transmittalNumber: data?.transmittalNumber,
    divisionName: String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A"),
  });
  const handleToggleViewModal = () => {
    setIsViewOpen((prev) => !prev);
  };

  const formik = useFormik({
    initialValues: {
      status: "",
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to receive this transmittal?");
      if (isConfirmed) {
        try {
          if (data?.latestFormTransmittalTrail?.id) {
            const payload = {
              ...values,
            };
            await putFormTransmittalTrailService(data.latestFormTransmittalTrail?.id, payload);
            toast.success("Received form successfully");
            resetForm();
            navigate(ROUTES.CLIFSAADMIN.usedForms.key);
          } else {
            toast.error("Failed to process form: Form Transmittal Trail ID is undefined");
          }
        } catch (error) {
          toast.error("Failed to approve form");
        }
      }
    },
  });

  const fetchForm = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getReturnedTransmittalTrailService(Number(id));
        if (response?.data) {
          setData(response.data);
        }
      }
    } catch (error) {
      toast.error("Failed to load. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);

  useEffect(() => {
    getUsers({ filter: filterUser });
  }, []);
  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">FOR RECEIVING DETAILS</Typography>

        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full flex flex-col gap-10">
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">RETURN DETAILS</div>
            </div>
            <div className="grid grid-cols-4 gap-4 pb-4">
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Return Transmittal No.</span>
                <p className="text-sm">{data?.transmittalNumber}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Area Returned</span>
                <p className="text-sm">{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Returned To</span>
                <p className="text-sm">{String(findItem(users, "id", Number(data?.releasedToId), undefined, (user) => `${user.firstname} ${user.middlename || ""} ${user.lastname}`) || "N/A")}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Date Returned</span>
                <p className="text-sm">{data?.returnedPads?.[0]?.returnedAt ? new Date(data?.returnedPads?.[0]?.returnedAt).toLocaleDateString("en-US") : "N/A"}</p>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Returned Via</span>
                <p className="text-sm">{String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Courier Service</span>
                <p className="text-sm">{String(findItem(area, "id", Number(data?.deliveredBy), "areaName") || "N/A")}</p>
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm text-[#474747CC]">Tracking No.</span>
                <p className="text-sm">{String(findItem(users, "id", Number(data?.releasedToId), undefined, (user) => `${user.firstname} ${user.middlename || ""} ${user.lastname}`) || "N/A")}</p>
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="divider divider-start font-semibold">SERIES OVERVIEW</div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-3 grid-flow-col gap-4">
                <div className="p-2">
                  <span className="text-sm text-[#474747CC]">Division</span>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    <p>{String(findItem(divisions, "id", Number(data?.returnedPads?.[0]?.form?.divisionId), "divisionName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Type</p>
                  <div className="border-b-2 border-slate-300 w-33 text-sm">
                    <p>{String(findItem(formTypes, "id", Number(data?.returnedPads?.[0]?.form?.formTypeId), "formTypeName") || "N/A")}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">AREA</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>{String(findItem(area, "id", Number(data?.returnedPads?.[0]?.form?.areaId), "areaName") || "N/A")}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300">
              <div className="mb-4 flex w-full">
                <div className="overflow-auto max-h-64 w-full">
                  <Table
                    className="!min-h-[100%] h-[300px] border-[1px] border-zinc-300"
                    columns={columns}
                    data={padDetails?.returnedPads || []}
                    searchable={false}
                    multiSelect={false}
                    selectable={false}
                    paginationTotalRows={data?.returnedPads?.length || 0}
                    paginationServer={true}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* View Modal */}
        <ViewPadDetailsModal
          isViewOpen={isViewOpen}
          isReturned={true}
          handleToggleViewModal={handleToggleViewModal}
          returnedPads={padDetails?.returnedPads?.find((pad) => pad.id === viewPadAssignmentId)}
          formData={data}
        />
        <div className="flex justify-center gap-2 mt-4">
          <Button
            type="submit"
            classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
            onClick={() => {
              formik.setFieldValue("status", FormStatus.RECEIVED);
              formik.handleSubmit();
            }}
          >
            Receive
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ViewReturnedFormReceiving;
